{"root": ["./src/App.tsx", "./src/main.tsx", "./src/components/TracingModal.tsx", "./src/components/theme-provider.tsx", "./src/components/theme-toggle.tsx", "./src/components/auth/ProtectedRoute.tsx", "./src/components/auth/__tests__/ProtectedRoute.test.tsx", "./src/components/dev/responsive-debugger.tsx", "./src/components/dev/responsive-validator.tsx", "./src/components/layout/Footer.tsx", "./src/components/layout/Header.tsx", "./src/components/layout/Layout.tsx", "./src/components/shared/data-table/data-table.tsx", "./src/components/ui/adaptive-grid.tsx", "./src/components/ui/advanced-pagination.tsx", "./src/components/ui/advanced-searchable-dropdown.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/command.tsx", "./src/components/ui/copy-button.tsx", "./src/components/ui/data-table.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/enhanced-data-table.tsx", "./src/components/ui/expandable-cell.tsx", "./src/components/ui/form.tsx", "./src/components/ui/hover-card.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/mobile-drawer.tsx", "./src/components/ui/popover.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/responsive-container.tsx", "./src/components/ui/responsive-form.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/searchable-dropdown.tsx", "./src/components/ui/select.tsx", "./src/components/ui/separator.tsx", "./src/components/ui/skeleton.tsx", "./src/components/ui/statistics.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/textarea.tsx", "./src/components/ui/tooltip.tsx", "./src/hooks/useResponsive.ts", "./src/hooks/useViewport.ts", "./src/lib/api.ts", "./src/lib/export.ts", "./src/lib/utils.ts", "./src/lib/__tests__/utils.test.ts", "./src/lib/stores/auth.ts", "./src/pages/HomePage.tsx", "./src/pages/about/AboutPage.tsx", "./src/pages/auth/GitHubOAuthPage.tsx", "./src/pages/auth/LarkOAuthPage.tsx", "./src/pages/auth/LoginPage.impl.tsx", "./src/pages/auth/LoginPage.tsx", "./src/pages/auth/PasswordResetConfirmPage.tsx", "./src/pages/auth/PasswordResetPage.tsx", "./src/pages/auth/RegisterPage.tsx", "./src/pages/auth/__tests__/LoginPage.test.tsx", "./src/pages/channels/ChannelsPage.tsx", "./src/pages/channels/EditChannelPage.tsx", "./src/pages/channels/__tests__/ChannelsPage.test.tsx", "./src/pages/chat/ChatPage.tsx", "./src/pages/dashboard/DashboardPage.test.ts", "./src/pages/dashboard/DashboardPage.tsx", "./src/pages/logs/LogsPage.tsx", "./src/pages/models/ModelsPage.tsx", "./src/pages/redemptions/EditRedemptionPage.tsx", "./src/pages/redemptions/RedemptionsPage.tsx", "./src/pages/settings/OperationSettings.tsx", "./src/pages/settings/OtherSettings.tsx", "./src/pages/settings/PersonalSettings.tsx", "./src/pages/settings/SettingsPage.tsx", "./src/pages/settings/SystemSettings.tsx", "./src/pages/tokens/EditTokenPage.tsx", "./src/pages/tokens/TokensPage.impl.tsx", "./src/pages/tokens/TokensPage.tsx", "./src/pages/topup/TopUpPage.tsx", "./src/pages/users/EditUserPage.tsx", "./src/pages/users/UsersPage.tsx", "./src/test/responsive-components.test.tsx", "./src/test/setup.ts"], "errors": true, "version": "5.8.3"}